<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Farmer Registration</title>
	<script src="https://cdn.tailwindcss.com"></script>
	<script>
		tailwind.config = {
			theme: {
				extend: {
					colors: {
						primary: '#2563eb',
						secondary: '#64748b'
					}
				}
			}
		}
	</script>
	<style type="text/tailwindcss">
		@layer components {
			.form-input {
				@apply w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary outline-none;
			}
			.form-label {
				@apply block text-sm font-medium text-gray-700 mb-1;
			}
			.btn {
				@apply px-4 py-2 rounded-lg font-medium transition-colors;
			}
			.btn-primary {
				@apply bg-primary text-white hover:bg-primary/90;
			}
			.section-card {
				@apply bg-gray-50 p-4 rounded-lg h-full;
			}
		}
	</style>
</head>
<body class="bg-gray-50">
	<div class="min-h-screen p-6">
		<div class="max-w-7xl mx-auto bg-white rounded-xl shadow-sm p-6">
			<h1 class="text-2xl font-bold text-gray-900 mb-6">Farmer Registration</h1>
			
			<form id="farmerForm" class="space-y-4">
				<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
					<div class="section-card">
						<h2 class="text-lg font-semibold text-gray-900 mb-4">Personal Information</h2>
						<div class="space-y-4">
							<div>
								<label class="form-label" for="fullName">Full Name</label>
								<input type="text" id="fullName" name="fullName" class="form-input" required>
							</div>
							<div>
								<label class="form-label" for="farmerId">Farmer ID</label>
								<input type="text" id="farmerId" name="farmerId" class="form-input" required>
							</div>
						</div>
					</div>

					<div class="section-card">
						<h2 class="text-lg font-semibold text-gray-900 mb-4">Geographic Information</h2>
						<div class="space-y-4">
							<div>
								<label class="form-label" for="village">Village</label>
								<input type="text" id="village" name="village" class="form-input" required>
							</div>
							<div>
								<label class="form-label" for="taluk">Taluk</label>
								<input type="text" id="taluk" name="taluk" class="form-input" required>
							</div>
							<div>
								<label class="form-label" for="district">District</label>
								<input type="text" id="district" name="district" class="form-input" required>
							</div>
							<div>
								<label class="form-label" for="state">State</label>
								<input type="text" id="state" name="state" class="form-input" required>
							</div>
							<div>
								<label class="form-label" for="visualId">Visual ID</label>
								<input type="text" id="visualId" name="visualId" class="form-input bg-gray-100" readonly>
							</div>
						</div>
					</div>

					<div class="section-card">
						<div class="flex justify-between items-center mb-4">
							<h2 class="text-lg font-semibold text-gray-900">Cattle Information</h2>
							<button type="button" id="addCattle" class="btn btn-primary">+ Add Cattle</button>
						</div>
						<div id="cattleContainer" class="space-y-4 max-h-[600px] overflow-y-auto"></div>
					</div>
				</div>

				<div class="flex justify-end items-center gap-4 pt-2 border-t">
					<div class="w-48">
						<select id="generateType" class="form-input">
							<option value="">Select Type</option>
							<option value="farm">Farm Data</option>
							<option value="cattle">Selected Cattle</option>
						</select>
					</div>
					<button type="button" id="generateBtn" class="btn btn-primary">Generate</button>
				</div>
			</form>

			<div id="chart-space" class="mt-4 border-t pt-4">
				<h2 class="text-xl font-semibold text-gray-900 mb-2">Milk Production Projection</h2>
				<div id="charts-grid" class="grid grid-cols-2 gap-4"></div>
			</div>
		</div>
	</div>

	<!-- Cattle Form Template (Hidden) -->
	<template id="cattleFormTemplate">
		<div class="cattle-form bg-white p-4 border rounded-lg">
			<div class="flex justify-between items-center mb-4">
				<div class="flex items-center gap-3">
					<input type="checkbox" class="cattle-select w-4 h-4 text-primary rounded border-gray-300 focus:ring-primary">
					<h3 class="text-md font-medium text-gray-900">Cattle #<span class="cattle-number"></span></h3>
				</div>
				<button type="button" class="remove-cattle text-red-500 hover:text-red-700">Remove</button>
			</div>
			<div class="space-y-4">
				<div>
					<label class="form-label">Cattle Type</label>
					<select name="cattleType[]" class="form-input" required>
						<option value="">Select Type</option>
						<option value="cow" selected>Cow</option>
						<option value="buffalo">Buffalo</option>
					</select>
				</div>
				<div>
					<label class="form-label">Breed</label>
					<input type="text" name="breed[]" class="form-input" required>
				</div>
				<div>
					<label class="form-label">Age (Years)</label>
					<input type="number" name="age[]" class="form-input" min="0" required>
				</div>
				<div>
					<label class="form-label">Data Collection Date</label>
					<input type="datetime-local" name="dataCollectionDate[]" class="form-input" required>
				</div>
				<div>
					<label class="form-label">Months Since Calving</label>
					<input type="number" name="monthsSinceCalving[]" class="form-input" min="0" max="120" required>
				</div>
				<div>
					<label class="form-label">Months Since Pregnancy</label>
					<input type="number" name="monthsSincePregnancy[]" class="form-input" min="0" max="120" required>
				</div>
				<div>
					<label class="form-label">Body Condition Score</label>
					<select name="bodyConditionScore[]" class="form-input" required>
						<option value="">Select Score</option>
						<option value="1.0">1.0 - Emaciated</option>
						<option value="1.5">1.5 - Very Thin</option>
						<option value="2.0">2.0 - Thin</option>
						<option value="2.5">2.5 - Slightly Thin</option>
						<option value="3.0" selected>3.0 - Good</option>
						<option value="3.5">3.5 - Above Average</option>
						<option value="4.0">4.0 - Fat</option>
						<option value="4.5">4.5 - Very Fat</option>
						<option value="5.0">5.0 - Obese</option>
					</select>
				</div>
				<div>
					<label class="form-label">Average Daily Milk (Liters)</label>
					<div class="flex gap-2">
						<div class="flex-1">
							<input type="number" name="milkProduction[]" class="form-input" min="0" step="0.1" required placeholder="Liters">
						</div>
						<div class="w-1/3">
							<select name="milkProductionMonth[]" class="form-input" required>
								<option value="">Month</option>
								<option value="1">January</option>
								<option value="2">February</option>
								<option value="3">March</option>
								<option value="4">April</option>
								<option value="5">May</option>
								<option value="6">June</option>
								<option value="7">July</option>
								<option value="8">August</option>
								<option value="9">September</option>
								<option value="10">October</option>
								<option value="11">November</option>
								<option value="12">December</option>
							</select>
						</div>
					</div>
				</div>
			</div>
		</div>
	</template>
	<!-- Scripts -->
	<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation"></script>
	<script src="https://d3js.org/d3.v7.min.js"></script>
	<script src="js/index.js"></script>
</body>
</html>