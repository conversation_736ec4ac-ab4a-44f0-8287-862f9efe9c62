// Chart generation functionality
function createMilkProductionChart(data) {
    const plotData = getPlotPoints(data);
    const currentYear = new Date().getFullYear();
    
    // Clear previous charts
    document.getElementById('charts-grid').innerHTML = '';
    
    // Create chart container
    const chartWrapper = document.createElement('div');
    chartWrapper.className = 'col-span-2 bg-white p-4 rounded-lg shadow';
    const chartId = `chart-${Date.now()}`;
    chartWrapper.innerHTML = `<canvas id="${chartId}"></canvas>`;
    document.getElementById('charts-grid').appendChild(chartWrapper);

    // Prepare data for Chart.js
    const labels = Object.keys(plotData.map);
    const dataPoints = labels.map(key => {
        const value = plotData.map[key];
        return typeof value === 'object' ? value.lpd : value;
    });

    // Get colors and messages for each point
    const colors = labels.map(key => {
        const value = plotData.map[key];
        return typeof value === 'object' ? value.color : '#2563eb';
    });

    const messages = labels.map(key => {
        const value = plotData.map[key];
        return typeof value === 'object' ? value.message : 'Production Data';
    });

    // Create the chart
    new Chart(document.getElementById(chartId), {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Milk Production (LPD)',
                data: dataPoints,
                borderColor: '#2563eb',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                fill: true,
                tension: 0.4,
                pointRadius: 6,
                pointHoverRadius: 8,
                pointBackgroundColor: colors
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: `Milk Production Forecast ${currentYear}`,
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const index = context.dataIndex;
                            const lpd = context.parsed.y.toFixed(2);
                            const message = messages[index];
                            return [`Production: ${lpd} LPD`, `Status: ${message}`];
                        }
                    }
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Month',
                        font: {
                            weight: 'bold'
                        }
                    },
                    grid: {
                        display: false
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'Liters Per Day (LPD)',
                        font: {
                            weight: 'bold'
                        }
                    },
                    beginAtZero: true
                }
            }
        }
    });
}

// Initialize event listeners after DOM content is loaded
document.addEventListener('DOMContentLoaded', function() {
    const generateBtn = document.getElementById('generateBtn');
    generateBtn.addEventListener('click', function() {
        const generateType = document.getElementById('generateType').value;
        if (!generateType) {
            alert('Please select a generation type');
            return;
        }

        // Get selected cattle data
        if (generateType === 'cattle') {
            const selectedCattle = document.querySelectorAll('.cattle-form .cattle-select:checked');
            if (selectedCattle.length === 0) {
                alert('Please select at least one cattle');
                return;
            }

            // Get the most recent cattle data
            const cattleForm = selectedCattle[0].closest('.cattle-form');
            const monthsSinceCalving = parseInt(cattleForm.querySelector('[name="monthsSinceCalving[]"]').value);
            const milkProduction = parseFloat(cattleForm.querySelector('[name="milkProduction[]"]').value);
            const productionMonth = parseInt(cattleForm.querySelector('[name="milkProductionMonth[]"]').value);

            // Calculate relative months
            const currentDate = new Date();
            const currentMonth = currentDate.getMonth() + 1;
            const calvingMonth = ((currentMonth - monthsSinceCalving) + 12) % 12 || 12;
            const pregnancyMonth = (calvingMonth - 9 + 12) % 12 || 12;

            // Generate chart data
            const chartData = {
                month_of_pregnancy: pregnancyMonth,
                month_of_calving: calvingMonth,
                month_of_avg_lpd: productionMonth,
                avg_lpd_of_month: milkProduction
            };

            createMilkProductionChart(chartData);
        }
    });
});
