const month_name ={
    1: "January",
    2: "February",      
    3: "March",
    4: "April",
    5: "May",
    6: "June",
    7: "July",
    8: "August",
    9: "September", 
    10: "October",
    11: "November",
    12: "December"
}
function createMilkProductionChart(dataArray) {
    const currentYear = new Date().getFullYear();
    
    // Clear previous charts
    document.getElementById('charts-grid').innerHTML = '';
    
    // Create chart container with improved styling
    const chartWrapper = document.createElement('div');
    chartWrapper.className = 'col-span-2 bg-white p-8 rounded-xl shadow-lg';
    chartWrapper.style.minHeight = '600px'; // Make chart taller
    chartWrapper.style.width = '100%'; // Full width
    const chartId = `chart-${Date.now()}`;
    chartWrapper.innerHTML = `<canvas id="${chartId}"></canvas>`;
    document.getElementById('charts-grid').appendChild(chartWrapper);

    // Generate plot data for each cattle
    const allPlotData = dataArray.map((data, index) => {
        const plotData = getPlotPoints(data);
        return {
            ...plotData,
            cattleId: data.id
        };
    });

    // Get unique labels (months) from all cattle
    const allLabels = new Set();
    allPlotData.forEach(plot => {
        Object.keys(plot.map).forEach(label => allLabels.add(label));
    });
    const labels = Array.from(allLabels).sort((a, b) => {
        return parseInt(a.split(' - ')[0]) - parseInt(b.split(' - ')[0]);
    });

    // Define a set of distinct colors for different cattle
    const lineColors = [
        '#3b82f6', // Blue
        '#ef4444', // Red
        '#22c55e', // Green
        '#f97316', // Orange
        '#8b5cf6', // Purple
        '#06b6d4', // Cyan
        '#ec4899', // Pink
        '#f59e0b', // Amber
    ];

    // Calculate year regions
    const yearBackgrounds = {};
    let startYear = allPlotData[0].start_year || new Date().getFullYear(); // Use first cattle's start year or current year as fallback
    let monthsPerYear = 12;
    let yearIndex = 0;
    
    // Create regions for 3 years (36 months of data)
    const yearColors = [
        'rgba(219, 234, 254, 0.2)',  // Light blue
        'rgba(254, 226, 226, 0.2)',  // Light red
        'rgba(220, 252, 231, 0.2)'   // Light green
    ];

    for (let i = 0; i < 3; i++) {
        yearBackgrounds[`year${startYear + i}`] = {
            type: 'box',
            xMin: yearIndex,
            xMax: yearIndex + monthsPerYear - 1,
            yMin: 'min',
            yMax: 'max',
            backgroundColor: yearColors[i],
            borderWidth: 0,
            drawTime: 'beforeDatasetsDraw',
            label: {
                display: true,
                content: (startYear + i).toString(),
                position: {x: 'start', y: 'start'},
                padding: {top: 4, left: 8},
                color: 'rgba(107, 114, 128, 0.8)',
                font: { 
                    size: 14,
                    weight: '500',
                    family: 'system-ui'
                }
            }
        };
        yearIndex += monthsPerYear;
    };

    // Prepare annotations for vertical lines
    const annotations = {};
    labels.forEach((label, index) => {
        // Check each cattle's data for events
        allPlotData.forEach((plotData, cattleIndex) => {
            const value = plotData.map[label];
            // Add vertical lines for special events
            if (typeof value === 'object' && value.message && value.message !== 'Production Data') {
                annotations[`line${index}-${cattleIndex}`] = {
                    type: 'line',
                    xMin: index,
                    xMax: index,
                    borderColor: lineColors[cattleIndex % lineColors.length],
                    borderWidth: 1,
                    borderDash: [5, 5],
                    borderDashOffset: cattleIndex * 2, // Offset dash pattern for each cattle
                };
            }
        });
        
        // Add year transition lines
        const month = parseInt(label.split(' - ')[0]);
        if (month % 12 === 1 && index > 0) { // Check if it's January (start of year)
            annotations[`yearTransition${index}`] = {
                type: 'line',
                xMin: index,
                xMax: index,
                borderColor: 'rgba(75, 85, 99, 0.6)', // Dark grey with 0.6 opacity
                borderWidth: 1.5,
            };
        }
    });

    // Create the chart
    new Chart(document.getElementById(chartId), {
        type: 'line',
        data: {
            labels: labels,
            datasets: allPlotData.map((plotData, index) => ({
                label: `Cattle #${plotData.cattleId}`,
                data: labels.map(label => {
                    const value = plotData.map[label];
                    return typeof value === 'object' ? value.lpd : value;
                }),
                borderColor: lineColors[index % lineColors.length],
                backgroundColor: 'transparent',
                borderWidth: 1.5, // Thinner lines
                fill: false,
                tension: 0.4,
                pointRadius: labels.map(label => {
                    const value = plotData.map[label];
                    return typeof value === 'object' && value.pointRadius ? value.pointRadius : 3;
                }),
                pointHoverRadius: labels.map(label => {
                    const value = plotData.map[label];
                    return typeof value === 'object' && value.pointRadius ? value.pointRadius + 2 : 5;
                }),
                pointStyle: labels.map(label => {
                    const value = plotData.map[label];
                    return typeof value === 'object' && value.pointStyle ? value.pointStyle : 'circle';
                }),
                pointBackgroundColor: labels.map(label => {
                    const value = plotData.map[label];
                    return typeof value === 'object' ? value.color : lineColors[index % lineColors.length];
                }),
                pointBorderColor: 'white',
                pointBorderWidth: 1,
                pointShadowColor: 'rgba(0,0,0,0.1)',
                pointShadowBlur: 3
            }))
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                annotation: {
                    common: {
                        drawTime: 'beforeDraw'
                    },
                    annotations: {
                        ...yearBackgrounds,  // Year backgrounds drawn first
                        ...annotations      // Vertical lines drawn on top
                    }
                },
                title: {
                    display: true,
                    text: `Milk Production Forecast ${currentYear}`,
                    font: {
                        size: 18,
                        weight: '500',
                        family: 'system-ui'
                    },
                    padding: 20
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const lpd = context.parsed.y;
                            if (lpd === null || lpd === undefined) return null;
                            
                            const datasetLabel = context.dataset.label;
                            const plotData = allPlotData[context.datasetIndex];
                            const label = labels[context.dataIndex];
                            const value = plotData.map[label];
                            
                            const message = typeof value === 'object' ? value.message : 'Production Data';
                            return [`${datasetLabel}: ${lpd.toFixed(2)} LPD`, `Status: ${message}`];
                        }
                    }
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Month',
                        font: {
                            size: 14,
                            weight: '500',
                            family: 'system-ui'
                        },
                        padding: {top: 15}
                    },
                    grid: {
                        color: 'rgba(226, 232, 240, 0.6)',
                        lineWidth: 1,
                        drawBorder: false
                    },
                    ticks: {
                        padding: 10,
                        font: {
                            size: 12,
                            family: 'system-ui'
                        }
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'Liters Per Day (LPD)',
                        font: {
                            size: 14,
                            weight: '500',
                            family: 'system-ui'
                        },
                        padding: {right: 15}
                    },
                    grid: {
                        color: 'rgba(226, 232, 240, 0.6)',
                        lineWidth: 1,
                        drawBorder: false,
                        drawTicks: false
                    },
                    ticks: {
                        padding: 10,
                        font: {
                            size: 12,
                            family: 'system-ui'
                        }
                    },
                    beginAtZero: true,
                    suggestedMax: Math.ceil(Math.max(
                        ...allPlotData.flatMap(plot => 
                            Object.values(plot.map)
                                .map(v => typeof v === 'object' ? v.lpd : v)
                                .filter(v => typeof v === 'number')
                        )
                    ) * 1.1) // Add 10% buffer to max value
                }
            },
            layout: {
                padding: {
                    top: 20,
                    right: 20,
                    bottom: 20,
                    left: 20
                }
            }
        }
    });
}

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    const generateBtn = document.getElementById('generateBtn');
    let cattleCount = 0;

    // Function to add a cattle form with optional prefill data
    function addCattleForm(prefillData = null) {
        const template = document.getElementById('cattleFormTemplate');
        const cattleContainer = document.getElementById('cattleContainer');
        
        // Clone the template
        const clone = template.content.cloneNode(true);
        cattleCount++;
        
        // Update the cattle number
        clone.querySelector('.cattle-number').textContent = cattleCount;
        
        // Prefill data if provided
        if (prefillData) {
            const form = clone.querySelector('.cattle-form');
            form.querySelector('[name="monthsSinceCalving[]"]').value = prefillData.month_since_calving;
            form.querySelector('[name="monthsSincePregnancy[]"]').value = prefillData.month_since_pregnancy;
            form.querySelector('[name="milkProduction[]"]').value = prefillData.avg_lpd_of_month;
            form.querySelector('[name="milkProductionMonth[]"]').value = prefillData.month_of_avg_lpd;
            
            // Set the data collection date
            const dateInput = form.querySelector('[name="dataCollectionDate[]"]');
            const now = prefillData.data_collection_date;
            const dateString = now.toISOString().slice(0, 16); // Format: YYYY-MM-DDThh:mm
            dateInput.value = dateString;
            
            // Check the checkbox for this cattle
            form.querySelector('.cattle-select').checked = true;

            // Set default values for other fields
            form.querySelector('[name="cattleType[]"]').value = 'cow';
            form.querySelector('[name="bodyConditionScore[]"]').value = '3.0';
        }
        
        // Add remove functionality
        clone.querySelector('.remove-cattle').addEventListener('click', function(e) {
            e.preventDefault();
            const cattleForm = this.closest('.cattle-form');
            cattleForm.remove();
        });
        
        // Add to container
        cattleContainer.appendChild(clone);
    }

    // Add initial prefilled forms with sample data
    const sampleCattle = [
        {
            month_since_pregnancy: 6,
            month_since_calving: 2,
            month_of_avg_lpd: 5,
            avg_lpd_of_month: 40,
            data_collection_date: new Date()
        },
        {
            month_since_pregnancy: 4,
            month_since_calving: 1,
            month_of_avg_lpd: 3,
            avg_lpd_of_month: 35,
            data_collection_date: new Date()
        },
        {
            month_since_pregnancy: 8,
            month_since_calving: 4,
            month_of_avg_lpd: 6,
            avg_lpd_of_month: 45,
            data_collection_date: new Date()
        }
    ];
    
    // Add each sample cattle form
    sampleCattle.forEach(prefillData => {
        addCattleForm(prefillData);
    });

    // Add Cattle Button Functionality
    document.getElementById('addCattle').addEventListener('click', function() {
        // Get the template
        const template = document.getElementById('cattleFormTemplate');
        if (!template) {
            console.error('Cattle form template not found');
            return;
        }
        
        // Clone the template
        const clone = template.content.cloneNode(true);
        cattleCount++;
        
        // Update the cattle number
        clone.querySelector('.cattle-number').textContent = cattleCount;

        // Initialize the form with default values
        const form = clone.querySelector('.cattle-form');
        if (!form) {
            console.error('Form element not found in template');
            return;
        }
        
        // Set default values for required fields
        form.querySelector('[name="cattleType[]"]').value = 'cow';
        form.querySelector('[name="breed[]"]').value = 'Holstein'; // Default breed
        form.querySelector('[name="age[]"]').value = '3'; // Default age
        form.querySelector('[name="bodyConditionScore[]"]').value = '3.0';
        form.querySelector('[name="monthsSinceCalving[]"]').value = '0';
        form.querySelector('[name="monthsSincePregnancy[]"]').value = '0';
        form.querySelector('[name="milkProduction[]"]').value = '0';
        form.querySelector('[name="milkProductionMonth[]"]').value = (new Date().getMonth() + 1).toString();

        // Set current date and time for data collection
        const dateInput = form.querySelector('[name="dataCollectionDate[]"]');
        const now = new Date();
        const dateString = now.toISOString().slice(0, 16); // Format: YYYY-MM-DDThh:mm
        dateInput.value = dateString;

        // Check the checkbox by default
        form.querySelector('.cattle-select').checked = true;
        
        // Add remove functionality
        clone.querySelector('.remove-cattle').addEventListener('click', function(e) {
            e.preventDefault();
            const cattleForm = this.closest('.cattle-form');
            cattleForm.remove();
        });
        
        // Add to container
        cattleContainer.appendChild(clone);
    });

    // Generate button click handler
    generateBtn.addEventListener('click', function() {
        // const generateType = document.getElementById('generateType').value;
        // if (!generateType) {
        //     alert('Please select a generation type');
        //     return;
        // }

        // Get selected cattle data
        // if (generateType === 'cattle') {
        //     const selectedCattle = document.querySelectorAll('.cattle-form .cattle-select:checked');
        //     if (selectedCattle.length === 0) {
        //         alert('Please select at least one cattle');
        //         return;
        //     }

        //     // Get the most recent cattle data
        //     const cattleForm = selectedCattle[0].closest('.cattle-form');
        //     const monthsSinceCalving = parseInt(cattleForm.querySelector('[name="monthsSinceCalving[]"]').value);
        //     const milkProduction = parseFloat(cattleForm.querySelector('[name="milkProduction[]"]').value);
        //     const productionMonth = parseInt(cattleForm.querySelector('[name="milkProductionMonth[]"]').value);

        //     // Calculate relative months
        //     const currentDate = new Date();
        //     const currentMonth = currentDate.getMonth() + 1;
        //     const calvingMonth = ((currentMonth - monthsSinceCalving) + 12) % 12 || 12;
        //     const pregnancyMonth = (calvingMonth - 9 + 12) % 12 || 12;

        //     // Generate chart data
        // }
        const selectedCattle = document.querySelectorAll('.cattle-form .cattle-select:checked');
        if (selectedCattle.length === 0) {
            alert('Please select at least one cattle');
            return;
        }

        // Get data from all selected cattle
        const cattleDataArray = Array.from(selectedCattle).map((checkbox, index) => {
            const cattleForm = checkbox.closest('.cattle-form');
            return {
                id: index + 1,
                month_since_pregnancy: parseInt(cattleForm.querySelector('[name="monthsSincePregnancy[]"]').value),
                month_since_calving: parseInt(cattleForm.querySelector('[name="monthsSinceCalving[]"]').value),
                month_of_avg_lpd: parseInt(cattleForm.querySelector('[name="milkProductionMonth[]"]').value),
                avg_lpd_of_month: parseFloat(cattleForm.querySelector('[name="milkProduction[]"]').value),
                data_collection_date: new Date(cattleForm.querySelector('[name="dataCollectionDate[]"]').value)
            };
        });

        createMilkProductionChart(cattleDataArray);
    });
});

function getPlotPoints(data) {
    const IDEAL_PREGNANCY_TRY_MONTH = 4; // Ideal pregnancy month
    let { data_collection_date, month_since_pregnancy, month_since_calving, month_of_avg_lpd, avg_lpd_of_month } = data
    
    date_of_pregnancy =  new Date(data_collection_date);
    date_of_calving = new Date(data_collection_date);

    date_of_pregnancy.setMonth(data_collection_date.getMonth() - month_since_pregnancy);
    date_of_calving.setMonth(data_collection_date.getMonth() - month_since_calving);

    month_since_pregnancy = date_of_pregnancy.getMonth() + 1;
    month_since_calving = date_of_calving.getMonth() + 1;
    console.log("data", {month_since_pregnancy, month_since_calving});
    if(date_of_pregnancy.getYear()<date_of_calving.getYear()){
        month_since_pregnancy = month_since_calving+IDEAL_PREGNANCY_TRY_MONTH;
    }

    const milk_reduction_rate = 0.93; // 7% reduction in milk production per month after calving
    let month_offset = month_since_calving + 1; // offset to account for the month of calving

    let avg_lpd_of_calving_month = calculateAvgLPDAfterCalvingMonth(month_of_avg_lpd, avg_lpd_of_month, month_since_calving, milk_reduction_rate, month_offset);
    let month = month_since_calving; // Start from the month of calving
    let plot = {
        start_year:date_of_calving.getFullYear(),
        x: [],
        y: [],
        map:{}
    }

    for (let cycle = 0; cycle < 1; cycle++) {
        let m_pregnancy = month_since_pregnancy;
        let m_calving = month_since_calving;
        let m_avg_lpd = month_of_avg_lpd;
        let y_value = undefined;
        let next_calving_month = month_since_pregnancy + 9 - 1; // 9 months after pregnancy
        plot = {
            start_year: date_of_calving.getFullYear(),
            x: [],
            y: [],
            map:{}
        }

        for (let m = 1; m <= 28; m++) {


            if (month === next_calving_month) {
                m_calving = next_calving_month;
                m_pregnancy = m_calving + IDEAL_PREGNANCY_TRY_MONTH;
                m_avg_lpd = month_of_avg_lpd;
                next_calving_month = m_calving + IDEAL_PREGNANCY_TRY_MONTH + 9 - 1;
                month_offset = m_calving + 1;

            }
            y_value = calculateAvgLpdOfTheMonth(month, {
                month_since_calving: m_calving,
                month_since_pregnancy: m_pregnancy,
                month_of_avg_lpd: m_avg_lpd,
                avg_lpd_of_calving_month: avg_lpd_of_calving_month,
                pregnancy_try_start_month: m_pregnancy,
                milk_reduction_rate,
                ideal_pregnancy_month: m_calving + 4,
                month_offset: month_offset
            });
            plot.x.push(month)
            plot.y.push((y_value));
            plot.map[`${month} - ${month_name[month%12===0?12:month%12]}`] = y_value;

            month++;

            
        }

    }

    return plot;
}

function equation(m) {

}

function calculateAvgLPDAfterCalvingMonth(month_of_avg_lpd, avg_lpd_of_month, calving_month, milk_reduction_rate, month_offset) {
    if (month_of_avg_lpd === calving_month) {
        return avg_lpd_of_month;
    }

    return avg_lpd_of_month / (Math.pow(milk_reduction_rate, month_of_avg_lpd - month_offset) * 2);
}

function calculateAvgLpdOfTheMonth(month, data) {
    const { month_since_calving, pregnancy_try_start_month, ideal_pregnancy_month, avg_lpd_of_calving_month, milk_reduction_rate,month_offset } = data;
    let is_dry_month;
    if (pregnancy_try_start_month) {
        is_dry_month = month >= pregnancy_try_start_month + 8-2 && month <= pregnancy_try_start_month + 8;
    }
    // console.log("is_dry_month", is_dry_month, month, pregnancy_try_start_month + 8, pregnancy_try_start_month + 8 + 1);
    // console.log("month", month, "month_since_calving", month_since_calving, "pregnancy_try_start_month", pregnancy_try_start_month, "ideal_pregnancy_month", ideal_pregnancy_month, "avg_lpd_of_calving_month", avg_lpd_of_calving_month, "milk_reduction_rate", milk_reduction_rate, "month_offset", month_offset);

    switch (true) {
        case month === month_since_calving:
            if(month === pregnancy_try_start_month){
                return {
                    lpd: avg_lpd_of_calving_month,
                    color: "red",
                    message: "Error! Cow calfed and pregnant",
                    pointRadius: 8, // Larger point for error
                    pointStyle: 'triangle', // Triangle for error
                    borderWidth: 2
                };
            }
            
            return {
                lpd: avg_lpd_of_calving_month,
                color: "pink",
                message: "Cow calfed!"
            };
        case month === pregnancy_try_start_month:
            return {
                lpd: 2 * avg_lpd_of_calving_month *
                    Math.pow(milk_reduction_rate, month - month_offset),
                color: "#2ef9ea",
                message: "Cow is pregnant!"
            };
        case month === ideal_pregnancy_month:
            return {
                lpd: 2 * avg_lpd_of_calving_month * Math.pow(milk_reduction_rate, month - month_offset),
                color: "blue",
                message: "Ideal Pregnancy Month"
            };
        case month < month_since_calving:
            return {
                lpd: 0,
                color: "grey",
                message: "off limit!"
            };

        case is_dry_month:
            return {
                lpd: 0,
                color: "red",
                message: "Cow went dry"
            };

        default:
            return {
                month:month_name[month%12===0?12:month%12],
                lpd: 2 * avg_lpd_of_calving_month * Math.pow(milk_reduction_rate, month - month_offset),
                color: "green",
                message: "Milk Production"
            };
    }
}